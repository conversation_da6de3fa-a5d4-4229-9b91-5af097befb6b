import Parser from 'web-tree-sitter';
// import Parser from 'tree-sitter';
import * as path from 'path';
import * as vscode from 'vscode';
import { languageIdToWasmLanguageMapping } from '@joycoder/shared';

export const languageCommentMarkers = {
  abap: {
    start: '"',
    end: '',
  },
  bat: {
    start: 'REM',
    end: '',
  },
  bibtex: {
    start: '%',
    end: '',
  },
  blade: {
    start: '#',
    end: '',
  },
  c: {
    start: '//',
    end: '',
  },
  clojure: {
    start: ';',
    end: '',
  },
  coffeescript: {
    start: '//',
    end: '',
  },
  cpp: {
    start: '//',
    end: '',
  },
  csharp: {
    start: '//',
    end: '',
  },
  css: {
    start: '/*',
    end: '*/',
  },
  dart: {
    start: '//',
    end: '',
  },
  dockerfile: {
    start: '#',
    end: '',
  },
  elixir: {
    start: '#',
    end: '',
  },
  erb: {
    start: '<%#',
    end: '%>',
  },
  erlang: {
    start: '%',
    end: '',
  },
  fsharp: {
    start: '//',
    end: '',
  },
  go: {
    start: '//',
    end: '',
  },
  groovy: {
    start: '//',
    end: '',
  },
  haml: {
    start: '-#',
    end: '',
  },
  handlebars: {
    start: '{{!',
    end: '}}',
  },
  haskell: {
    start: '--',
    end: '',
  },
  html: {
    start: '<!--',
    end: '-->',
  },
  ini: {
    start: ';',
    end: '',
  },
  java: {
    start: '//',
    end: '',
  },
  javascript: {
    start: '//',
    end: '',
  },
  javascriptreact: {
    start: '//',
    end: '',
  },
  jsonc: {
    start: '//',
    end: '',
  },
  jsx: {
    start: '//',
    end: '',
  },
  julia: {
    start: '#',
    end: '',
  },
  kotlin: {
    start: '//',
    end: '',
  },
  latex: {
    start: '%',
    end: '',
  },
  less: {
    start: '//',
    end: '',
  },
  lua: {
    start: '--',
    end: '',
  },
  makefile: {
    start: '#',
    end: '',
  },
  markdown: {
    start: '[]: #',
    end: '',
  },
  'objective-c': {
    start: '//',
    end: '',
  },
  'objective-cpp': {
    start: '//',
    end: '',
  },
  perl: {
    start: '#',
    end: '',
  },
  php: {
    start: '//',
    end: '',
  },
  powershell: {
    start: '#',
    end: '',
  },
  pug: {
    start: '//',
    end: '',
  },
  python: {
    start: '#',
    end: '',
  },
  ql: {
    start: '//',
    end: '',
  },
  r: {
    start: '#',
    end: '',
  },
  razor: {
    start: '<!--',
    end: '-->',
  },
  ruby: {
    start: '#',
    end: '',
  },
  rust: {
    start: '//',
    end: '',
  },
  sass: {
    start: '//',
    end: '',
  },
  scala: {
    start: '//',
    end: '',
  },
  scss: {
    start: '//',
    end: '',
  },
  shellscript: {
    start: '#',
    end: '',
  },
  slim: {
    start: '/',
    end: '',
  },
  solidity: {
    start: '//',
    end: '',
  },
  sql: {
    start: '--',
    end: '',
  },
  stylus: {
    start: '//',
    end: '',
  },
  svelte: {
    start: '<!--',
    end: '-->',
  },
  swift: {
    start: '//',
    end: '',
  },
  terraform: {
    start: '#',
    end: '',
  },
  tex: {
    start: '%',
    end: '',
  },
  typescript: {
    start: '//',
    end: '',
  },
  typescriptreact: {
    start: '//',
    end: '',
  },
  vb: {
    start: "'",
    end: '',
  },
  verilog: {
    start: '//',
    end: '',
  },
  'vue-html': {
    start: '<!--',
    end: '-->',
  },
  vue: {
    start: '//',
    end: '',
  },
  xml: {
    start: '<!--',
    end: '-->',
  },
  xsl: {
    start: '<!--',
    end: '-->',
  },
  yaml: {
    start: '#',
    end: '',
  },
};
export const dontAddLanguageMarker = ['php', 'plaintext'];
export const shebangLines = {
  html: '<!DOCTYPE html>',
  python: '#!/usr/bin/env python3',
  ruby: '#!/usr/bin/env ruby',
  shellscript: '#!/bin/sh',
  yaml: '# YAML data',
};
/**
 * 判断给定的源代码是否包含语言标记。
 * @param source 源代码字符串
 * @returns 如果源代码以 '#!' 或 '<!DOCTYPE' 开头，则返回 true；否则返回 false。
 */
export function hasLanguageMarker({ source: source }) {
  return source.startsWith('#!') || source.startsWith('<!DOCTYPE');
}
/**
 * 判断给定源代码是否以指定语言的注释标记开头
 * @param source 源代码字符串
 * @param languageId 语言标识符
 * @returns 如果源代码以注释标记开头则返回true，否则返回false
 */
export function hasCommentMarker({ source, languageId }) {
  const markers = languageCommentMarkers[languageId];
  if (markers && source) {
    const marker = markers.start;
    return source.startsWith(marker);
  }
  return false;
}
/**
 * 注释函数
 * @param text
 * @param languageId
 * @returns
 */
export function comment(text, languageId) {
  const markers = languageCommentMarkers[languageId];
  if (markers) {
    const end = markers.end == '' ? '' : ' ' + markers.end;
    return `${markers.start} ${text}${end}`;
  }
  return '';
}
/**
 * 获取语言标记
 * @param doc
 */
export function getLanguageMarker(doc) {
  const { languageId: languageId } = doc;
  return dontAddLanguageMarker.indexOf(languageId) === -1 && !hasLanguageMarker(doc)
    ? languageId in shebangLines
      ? shebangLines[languageId]
      : comment(`Language: ${languageId}`, languageId)
    : '';
}

/**
 * 将文本按行注释，并返回注释后的文本
 * @param text 要注释的文本
 * @param languageId 语言标识符
 * @returns 注释后的文本
 */
export function commentBlockAsSingles(text, languageId) {
  if (!languageCommentMarkers[languageId] || text === '') return '';
  const trailingNewline = text.endsWith(`
`);
  const commented = (trailingNewline ? text.slice(0, -1) : text)
    .split(
      `
`
    )
    .map((line) => comment(line, languageId)).join(`
`);
  return trailingNewline
    ? commented +
        `
`
    : commented;
}
/**
 * 获取路径标记
 * @param doc
 */
export function getPathMarker(doc) {
  return doc.relativePath ? comment(`Path: ${doc.relativePath}`, doc.languageId) : '';
}
/**
 * 判断字符串是否以换行符结尾，如果不是则在末尾添加换行符
 * @param str 输入的字符串
 * @returns 处理后的字符串
 */
export function newLineEnded(str) {
  return str === '' ||
    str.endsWith(`
  `)
    ? str
    : str + `\n`;
}

/**
 * 检查给定的语言ID是否受支持
 * @param languageId 要检查的语言ID
 * @returns 如果语言ID受支持则返回true，否则返回false
 */
export function isSupportedLanguageId(languageId) {
  return languageId in languageIdToWasmLanguageMapping;
}
export function languageIdToWasmLanguage(languageId) {
  if (!(languageId in languageIdToWasmLanguageMapping)) throw new Error(`Unrecognized language: ${languageId}`);
  return languageIdToWasmLanguageMapping[languageId];
}
export function getBlockCloseToken(language) {
  switch (languageIdToWasmLanguage(language)) {
    case 'python':
      return null;
    case 'javascript':
    case 'typescript':
    case 'tsx':
    case 'go':
    case 'java':
      return '}';
    case 'ruby':
      return 'end';
  }
}
/**
 * 执行内部查询并返回匹配结果。
 * @param queries 查询数组，每个查询包含一个查询字符串和一个可选的预编译查询对象。
 * @param root 查询的根节点，包含树和语言信息。
 * @returns 匹配结果数组。
 */
export function innerQuery(queries, root) {
  const matches: any = [];
  for (let index = 0; index < queries.length; index++) {
    const query = queries[index];
    if (!query[1]) {
      try {
        const lang = root.tree.getLanguage();
        query[1] = lang.query(query[0]);
      } catch (error) {
        console.error('%c [ error ]-423', 'font-size:13px; background:pink; color:#bf2c9f;', error);
      }
    }
    matches.push(...query[1].matches(root));
  }
  // for (const query of queries) {
  // }
  return matches;
}
/**
 * 根据语言和根节点查询函数。
 * @param language - 目标编程语言。
 * @param root - 查询的根节点。
 * @returns 查询结果。
 */
export function queryFunctions(language, root) {
  const activeTextEditor = vscode.window.activeTextEditor;
  if (activeTextEditor) {
    const documentLanguageId: string = activeTextEditor.document.languageId;
    if (documentLanguageId === 'vue') {
      const queries = vueFunctionsQuery[languageIdToWasmLanguage(language)];
      return innerQuery(queries, root);
    }
  }
  const queries = functionQuery[languageIdToWasmLanguage(language)];
  return innerQuery(queries, root);
}
export function queryImports(language, root) {
  const queries = importsQuery[languageIdToWasmLanguage(language)];
  return innerQuery(queries, root);
}
export function queryExports(language, root) {
  const queries = exportsQuery[languageIdToWasmLanguage(language)];
  return innerQuery(queries, root);
}
export function queryGlobalVars(language, root) {
  const queries = globalVarsQuery[languageIdToWasmLanguage(language)];
  return innerQuery(queries, root);
}
export function queryPythonIsDocstring(blockNode) {
  return innerQuery([docstringQuery], blockNode).length == 1;
}
export function getAncestorWithSiblingFunctions(language, nd) {
  const check = isFunctionParent[languageIdToWasmLanguage(language)];
  for (; nd.parent; ) {
    if (check(nd.parent)) return nd;
    nd = nd.parent;
  }
  return nd.parent ? nd : null;
}
/**
 * 判断节点类型是否为函数类型。
 * @param language - 语言标识符。
 * @param nd - 节点对象。
 * @returns 如果节点类型为函数类型则返回true，否则返回false。
 */
export function isFunction(language, nd) {
  return functionTypes[languageIdToWasmLanguage(language)].has(nd.type);
}
/**
 * 判断节点是否为函数定义
 * @param language - 语言类型
 * @param nd - 节点对象
 * @returns 如果是函数定义返回true，否则返回false
 */
export function isFunctionDefinition(language, nd) {
  switch (languageIdToWasmLanguage(language)) {
    case 'python':
    case 'go':
    case 'ruby':
      return isFunction(language, nd);
    case 'javascript':
    case 'typescript':
    case 'tsx':
      if (
        nd.type === 'function_declaration' ||
        nd.type === 'generator_function_declaration' ||
        nd.type === 'method_definition'
      ) {
        return true;
      }
      if (nd.type === 'lexical_declaration' || nd.type === 'variable_declaration') {
        if (nd.namedChildCount > 1) return !1;
        const declarator = nd.namedChild(0);
        if (declarator == null) return !1;
        const init = declarator.namedChild(1);
        return init !== null && isFunction(language, init);
      }
      if (nd.type === 'expression_statement') {
        const expr = nd.namedChild(0);
        if (expr?.type === 'assignment_expression') {
          const rhs = expr.namedChild(1);
          return rhs !== null && isFunction(language, rhs);
        }
      }
      return false;
    case 'java':
      return nd.type === 'method_declaration';
  }
}
/**
 * 获取节点的前一个注释节点
 * @param nd 要查找的节点
 * @returns 如果找到注释节点则返回该节点，否则返回null
 */
export function getFirstPrecedingComment(nd) {
  let cur = nd;
  for (; cur.previousSibling?.type === 'comment'; ) {
    const prev = cur.previousSibling;
    if (prev.endPosition.row < cur.startPosition.row - 1) break;
    cur = prev;
  }
  return cur?.type === 'comment' ? cur : null;
}
export const vueFunctionQuery = `[
  (function_declaration)
  (method_definition)
  (variable_declarator
    name: (identifier)
    value: (arrow_function)
  )
  (variable_declarator
    name: (identifier)
    value: (generator_function)
  )
  (variable_declarator
    name: (identifier)
    value: (expression)
  )
  (field_definition
    value: (expression)
  )
  (function_declaration
    body: (statement_block) @body
  )
] @methods`;
export const jsFunctionQuery = `[
  (function_declaration (statement_block) @body)
  (function_declaration body: (statement_block) @body)
  (generator_function body: (statement_block) @body)
  (generator_function_declaration body: (statement_block) @body)
  (method_definition body: (statement_block) @body)
  (arrow_function body: (statement_block) @body)
  (class_declaration body: (class_body ("{") @class.body.cursor) @class.body)
  (comment) @comment
] @function`;
export const vueFunctionsQuery = {
  javascript: [[vueFunctionQuery]],
  typescript: [[jsFunctionQuery]],
};
export const functionQuery = {
  python: [
    [
      `(function_definition body: (block
           (expression_statement (string))? @docstring) @body) @function`,
    ],
    ['(ERROR ("def" (identifier) (parameters))) @function'],
  ],
  javascript: [[jsFunctionQuery]],
  typescript: [[jsFunctionQuery]],
  tsx: [[jsFunctionQuery]],
  go: [
    [
      `[
          (function_declaration body: (block) @body)
          (method_declaration body: (block) @body)
        ] @function`,
    ],
  ],
  ruby: [
    [
      `[
          (method name: (_) parameters: (method_parameters)? @params [(_)+ "end"] @body)
          (singleton_method name: (_) parameters: (method_parameters)? @params [(_)+ "end"] @body)
        ] @function`,
    ],
  ],
  java: [
    [
      `(method_declaration
        name: (identifier) @functionName
        parameters: (formal_parameters) @params
        body: (block) @body
      ) @function`,
    ],
  ],
};
export const requireCall = '(call_expression function: ((identifier) @req (#eq? @req "require")))';
export const declaratorWithRequire = `(variable_declarator value: ${requireCall})`,
  commonJsImport = `
  (lexical_declaration ${declaratorWithRequire}+)
  (variable_declaration ${declaratorWithRequire}+)
`;
export const tsImportQueries = [
  [`(program [ ${commonJsImport} ] @import)`],
  ['(program [ (import_statement) (import_alias) ] @import)'],
];
export const importsQuery = {
  python: [
    ['(module (future_import_statement) @import)'],
    ['(module (import_statement) @import)'],
    ['(module (import_from_statement) @import)'],
  ],
  javascript: [[`(program [ ${commonJsImport} ] @import)`], ['(program [ (import_statement) ] @import)']],
  typescript: tsImportQueries,
  tsx: tsImportQueries,
  go: [],
  ruby: [],
  java: [
    [
      // Match import statements
      `(import_declaration
        (scoped_identifier) @importIdentifier
      )`,
    ],
  ],
};
export const jsExportQueries = [['(program (export_statement) @export)']],
  exportsQuery = {
    python: [],
    javascript: jsExportQueries,
    typescript: jsExportQueries,
    tsx: jsExportQueries,
    go: [],
    ruby: [],
    java: [
      [
        // Match public classes
        `(class_declaration
          (modifiers
            (modifier) @publicModifier
          )
          name: (identifier) @className
          body: (class_body)
        )`,
      ],
      [
        // Match public methods
        `(method_declaration
          (modifiers
            (modifier) @publicModifier
          )
          name: (identifier) @methodName
          body: (block)
        )`,
      ],
    ],
  };
export const globalVarsQuery = {
  python: [['(module (global_statement) @globalVar)'], ['(module (expression_statement) @globalVar)']],
  javascript: [],
  typescript: [],
  tsx: [],
  go: [],
  ruby: [],
  java: [
    [
      `(class_body
        (field_declaration
          (variable_declarator
            name: (identifier) @globalVar
          )
        )
      )`,
    ],
  ],
};
export const jsFunctionTypes = [
  'function',
  'function_declaration',
  'generator_function',
  'generator_function_declaration',
  'method_definition',
  'arrow_function',
  'class_declaration',
  'comment',
];
export const javaFunctionTypes = [
  'method_declaration', // Regular method declaration in a class or interface
  'constructor_declaration', // Constructor declaration in a class
  'lambda_expression', // Lambda expression (anonymous function)
  'annotation_method_declaration', // Method declaration within an annotation type
  'interface_method_declaration', // Method declaration within an interface
  'comment', // Comments (for completeness, though not a function type)
];

export const functionTypes = {
  python: new Set(['function_definition']),
  javascript: new Set(jsFunctionTypes),
  typescript: new Set(jsFunctionTypes),
  tsx: new Set(jsFunctionTypes),
  go: new Set(['function_declaration', 'method_declaration']),
  ruby: new Set(['method', 'singleton_method']),
  java: new Set(javaFunctionTypes),
};
export const docstringQuery = [
  `[
    (class_definition (block (expression_statement (string))))
    (function_definition (block (expression_statement (string))))
]`,
];
export const callSiteQuery = {
  python: [
    [
      `(call
        function:  [
            (identifier) @caller
            (attribute attribute:(identifier) @caller)
        ]
        arguments: (argument_list) @args
    )`,
    ],
  ],
  javascript: [],
  tsx: [],
  typescript: [],
  go: [],
  ruby: [],
  java: [
    [
      `(method_invocation
        object: (identifier) @caller
        name: (identifier) @method
        arguments: (argument_list) @args
      )`,
    ],
  ],
};
export const isFunctionParent = {
  python: (nd) => nd.type === 'module' || (nd.type === 'block' && nd.parent?.type === 'class_definition'),
  javascript: (nd) => nd.type === 'program' || nd.type === 'class_body',
  typescript: (nd) => nd.type === 'program' || nd.type === 'class_body',
  tsx: (nd) => nd.type === 'program' || nd.type === 'class_body',
  go: (nd) => nd.type === 'source_file',
  ruby: (nd) => nd.type === 'program' || nd.type === 'class',
  java: (nd) => nd.type === 'class_body' || nd.type === 'interface_body',
};
export const loadedLanguages = new Map();
export async function loadWasmLanguage(language) {
  // await Parser.init();
  const wasmInitFile = path.resolve(__dirname, '..', 'assets/wasm/', `tree-sitter.wasm`);
  await Parser.init({
    locateFile(path) {
      if (path.endsWith('.wasm')) {
        return wasmInitFile;
      }
      return path;
    },
  });
  const wasmFile = path.resolve(__dirname, '..', 'assets/wasm/', `tree-sitter-${language}.wasm`);
  try {
    // eslint-disable-next-line
    return (Parser as any).Language.load(wasmFile);
  } catch (e) {
    if ((e.code === 'ENOENT' || e.code === 'EIO' || e.code === 'EACCES' || e.code == 'EPERM') && e instanceof Error) {
      const error: Error | any = new Error(`Could not load tree-sitter-${language}.wasm`);
      throw ((error.code = 'CopilotPromptLoadFailure'), error);
    }
    throw e;
  }
}
async function getLanguage(language) {
  const wasmLanguage = languageIdToWasmLanguage(language);
  if (!loadedLanguages.has(wasmLanguage)) {
    const loadedLang = await loadWasmLanguage(wasmLanguage);
    loadedLanguages.set(wasmLanguage, loadedLang);
  }
  return loadedLanguages.get(wasmLanguage);
}
/**
 * 安全地调用 tree-sitter 节点的方法，防止在生产环境中方法被压缩器重命名导致的错误
 * @param node tree-sitter 节点
 * @param methodName 方法名
 * @returns 方法调用结果，如果方法不存在则返回 false
 */
export function safeCallNodeMethod(node: any, methodName: string): boolean {
  if (!node || typeof node[methodName] !== 'function') {
    return false;
  }
  try {
    return node[methodName]();
  } catch (error) {
    console.warn(`Failed to call ${methodName} on tree-sitter node:`, error);
    return false;
  }
}

export async function parseTreeSitter(language, source) {
  const treeSitterLanguage = await getLanguage(language);
  // eslint-disable-next-line
  const parser: any = new Parser();
  parser.setLanguage(treeSitterLanguage);
  const parsedTree = parser.parse(source);
  return parser.delete(), parsedTree;
}
/**
 * 获取指定节点开始位置的缩进字符串。
 * @param srcString 源字符串。
 * @param node 包含startIndex属性的节点，表示节点的开始位置。
 * @returns 从节点开始位置向前查找，直到非空格或制表符为止的字符串。
 */
export function getIndentation(srcString, node) {
  let i = node.startIndex - 1;
  for (; i >= 0 && (srcString[i] === ' ' || srcString[i] === '	'); ) i--;
  if (
    i < 0 ||
    srcString[i] ===
      `
`
  )
    return srcString.substring(i + 1, node.startIndex);
}
/**
 * 从源字符串中提取 TypeScript 函数声明或签名。
 * @param srcString 源代码字符串
 * @param defn 函数定义节点
 * @returns 提取的函数声明或签名字符串，如果无法提取则返回空字符串
 */
export function extractTypeScriptFunctionDeclaration(srcString, defn) {
  const endIndex = defn.childForFieldName('return_type')?.endIndex ?? defn.childForFieldName('parameters')?.endIndex;
  if (endIndex !== void 0) {
    const signature = srcString.substring(defn.startIndex, endIndex) + ';';
    return defn.type === 'function_declaration' || defn.type === 'function_signature'
      ? 'declare ' + signature
      : signature;
  }
  return '';
}
/**
 * 提取TypeScript成员声明。
 *
 * @param srcString - 源代码字符串。
 * @param defn - 成员定义节点。
 * @returns 提取出的成员声明字符串，若不符合条件则返回空字符串。
 */
export function extractTypeScriptMemberDeclaration(srcString, defn) {
  if (defn?.firstChild?.type === 'accessibility_modifier' && defn.firstChild.text === 'private') return '';
  const commentNode = getFirstPrecedingComment(defn),
    indentation = getIndentation(srcString, commentNode ?? defn) ?? '  ',
    docComment = getDocComment(srcString, defn);
  switch (defn.type) {
    case 'ambient_declaration':
      const inner = defn.namedChild(0);
      return inner ? indentation + docComment + extractTypeScriptMemberDeclaration(srcString, inner) : '';
    case 'method_definition':
    case 'method_signature':
      return indentation + docComment + extractTypeScriptFunctionDeclaration(srcString, defn);
    case 'public_field_definition': {
      const endIndex = defn.childForFieldName('type')?.endIndex ?? defn.childForFieldName('name')?.endIndex;
      if (endIndex !== void 0) return indentation + docComment + srcString.substring(defn.startIndex, endIndex) + ';';
    }
  }
  return '';
}
/**
 * 获取给定偏移量之后的同级函数的起始位置。
 *
 * @param {object} params - 包含源代码、偏移量和语言ID的对象。
 * @param {string} params.source - 源代码字符串。
 * @param {number} params.offset - 指定的偏移量。
 * @param {string} params.languageId - 语言ID。
 * @returns {Promise<number>} - 同级函数的起始位置或原偏移量。
 */
export async function getSiblingFunctionStart({ source: source, offset: offset, languageId: languageId }) {
  if (isSupportedLanguageId(languageId)) {
    const tree = await parseTreeSitter(languageId, source);
    try {
      let startingOffset = offset;
      for (; startingOffset >= 0 && /\s/.test(source[startingOffset]); ) startingOffset--;
      const nd = tree.rootNode.descendantForIndex(startingOffset),
        anc = getAncestorWithSiblingFunctions(languageId, nd);
      if (anc) {
        for (let sibling = anc.nextSibling; sibling; sibling = sibling.nextSibling)
          if (isFunctionDefinition(languageId, sibling)) {
            const startIndex = getFirstPrecedingComment(sibling)?.startIndex ?? sibling.startIndex;
            if (startIndex < offset) continue;
            return startIndex;
          }
        if (anc.endIndex >= offset) return anc.endIndex;
      }
    } finally {
      tree.delete();
    }
  }
  return offset;
}
/**
 * 获取指定节点前最近的文档注释。
 * @param srcString 源代码字符串。
 * @param node 目标节点。
 * @returns 如果存在，返回文档注释字符串；否则，返回空字符串。
 */
export async function getDocComment(srcString, node) {
  const docCommentNode = getFirstPrecedingComment(node);
  return docCommentNode ? srcString.substring(docCommentNode.startIndex, node.startIndex) : '';
}
/**
 * 从源字符串中提取函数注释
 *
 * @param languageId 语言ID
 * @param srcString 源字符串
 * @returns 返回提取到的函数注释，如果未找到则返回空字符串
 */
export async function extractFunctionComments(languageId, srcString) {
  try {
    const node = await parseTreeSitter(languageId, srcString);
    let docComment = await getDocComment(srcString, node?.rootNode);
    if (!docComment) {
      const functionNodes = queryFunctions(languageId, node.rootNode);
      if (functionNodes.length > 0) {
        for (let index = 0; index < functionNodes.length; index++) {
          const element = functionNodes[index];
          const docstringNode = element?.captures?.find((c) => c.name === 'docstring')?.node;
          if (docstringNode) {
            docComment = docstringNode.text ?? '';
            break;
          }
        }
      } else {
        docComment = srcString;
      }
    }
    return docComment ?? '';
  } catch (error) {
    return '';
  }
}
/**
 * 解析给定的源代码，使用指定的语言进行解析，并返回解析结果是否没有错误。
 * @param {string} language - 解析使用的语言。
 * @param {string} source - 要解析的源代码。
 * @returns {boolean} - 解析结果是否没有错误。
 */
export async function parsesWithoutError(language, source) {
  const tree = await parseTreeSitter(language, source);
  const result = !safeCallNodeMethod(tree.rootNode, 'hasError');
  return tree.delete(), result;
}
/**
 * 检查给定的字符串是否包含HTML或CSS代码。
 * @param source 待检查的字符串。
 * @returns 如果字符串包含HTML或CSS代码，则返回true；否则返回false。
 */
export async function parsesHtmlOrCSS(source, backList) {
  if (backList.includes('HTML') && backList.includes('CSS')) {
    const htmlReg = /<[^>]+>/;
    const cssRegex = /^\s*\.[-\w]+\s*{?\s*|\s*[\w-]+\s*:\s*[\w-]+\s*;/;
    return htmlReg.test(source) || cssRegex.test(source);
  } else if (backList.includes('HTML')) {
    const htmlReg = /<[^>]+>/;
    return htmlReg.test(source);
  } else if (backList.includes('CSS')) {
    const cssRegex = /^\s*\.[-\w]+\s*{?\s*|\s*[\w-]+\s*:\s*[\w-]+\s*;/;
    // const cssRegex =                  /\s*[\w-]+\s*:\s*[\w-]+\s*;/;
    source = source?.trim();
    return cssRegex.test(source);
  } else if (backList.includes('SQL')) {
    return true;
  }
  return false;
}
/**
 * 解析源代码中的import语句。
 *
 * @param source 源代码字符串。
 * @returns 返回一个包含所有import来源模块路径的数组。
 */
export function parseImportLines(source) {
  // 判断是不是import开头
  const importLines = source.split('\n').filter((line) => line.trim().startsWith('import'));
  const imports = importLines.map((line) => {
    const match = line.match(/^import\s+({\s*[^}]+\s*}|\w+)\s+from\s+['"]([^'"]+)['"]/);
    return match ? match[1] : null;
  });
  return imports.filter((item) => !!item);
}
/**
 * 检查给定的字符串是否包含HTML或CSS代码。
 * @param source 待检查的字符串。
}
/**
 * 解析指定语言的源代码并检查函数节点是否存在错误或缺失。
 *
 * @async
 * @param {string} language - 要解析的语言。
 * @param {string} source - 要解析的源代码。
 * @return {TreeNode[]} 返回存在错误或缺失的函数节点，若无则返回空数组。
 */
export async function parsesWithoutMissLine(language, source) {
  try {
    const tree = await parseTreeSitter(language, source);
    const nodes = queryFunctions(language, tree.rootNode).map((res) => {
      // 检查是否闭合
      const functionNode = res.captures.find((c) => c.name === 'function').node;
      const isClosed =
        functionNode.endPosition.row > functionNode.startPosition.row ||
        (functionNode.endPosition.row === functionNode.startPosition.row &&
          functionNode.endPosition.column > functionNode.startPosition.column);
      const hasError = safeCallNodeMethod(functionNode, 'hasError');
      const isMissing = safeCallNodeMethod(functionNode, 'isMissing');
      const result = hasError || isMissing || !isClosed;
      return result ? functionNode : null;
    });
    tree.delete();
    return nodes.filter((item) => !!item);
  } catch (error) {
    return [];
  }
}
/**
 * 获取源代码中所有函数的起始和结束位置。
 * @param {string} language - 源代码的编程语言。
 * @param {string} source - 源代码文本。
 * @returns {Promise<Array>} 包含函数位置的对象数组，每个对象有startIndex和endIndex属性。
 */
export async function getFunctionPositions(language, source) {
  try {
    const tree = await parseTreeSitter(language, source);
    const positions = queryFunctions(language, tree.rootNode).map((res) => {
      const fn = res.captures.find(
        (c) => c && (c.name === 'function' || (c.name === 'methods' && c?.node?.type == 'method_definition'))
      )?.node;
      if (!fn || fn.type == 'comment') {
        return null;
      }
      const node = tree.rootNode.descendantForPosition(fn.startPosition, fn.endPosition);
      const selection: vscode.Selection = new vscode.Selection(
        fn.startPosition.row,
        0,
        fn.endPosition.row,
        fn.endPosition.column + 1
      );
      const editor = vscode.window.activeTextEditor;
      const textCursor = editor?.document?.getText(selection);
      const languageId = editor?.document?.languageId;
      const builtInFunctionList = [
        'call_expression',
        'pair',
        'arguments',
        'assignment_expression',
        'lexical_declaration',
        'return_statement',
        'comment',
        'jsx_expression',
      ];
      if (languageId !== 'vue') {
        builtInFunctionList.push('object');
      }
      const builtInFunction = builtInFunctionList.includes(node.parent.type); // 回调函数
      return {
        builtInFunction,
        functionContent: textCursor,
        startPosition: fn.startPosition,
        endPosition: fn.endPosition,
        startIndex: fn.startIndex,
        endIndex: fn.endIndex,
      };
    });
    const psts = positions.filter((item) => !!item);
    return tree.delete(), psts;
  } catch (error) {
    console.info('tree-error', error?.message || '');
    return [];
  }
}

/**
 * 获取调用位置信息的异步函数。
 * @param {Object} docInfo - 文档信息对象。
 * @returns {Array} - 调用位置信息数组。
 */
export async function getCallSites(docInfo) {
  if (!(docInfo.languageId in callSiteQuery)) return [];
  let offset = docInfo.offset;
  let source = docInfo.getText().substring(0, offset);
  // let source = docInfo.source.substring(0, offset);
  const pretruncateOffset = Math.max(source.length - 5000, 0);
  const linesBeforeTruncation =
    source.substring(0, pretruncateOffset).split(`
`).length - 1;
  (offset -= pretruncateOffset), (source = source.substring(pretruncateOffset)), (source = source + ')))))');
  const callers: any = [],
    tree = await parseTreeSitter(docInfo.languageId, source),
    queries = callSiteQuery[languageIdToWasmLanguageMapping[docInfo.languageId]];
  return (
    innerQuery(queries, tree.rootNode).forEach((res, resIndex) => {
      let callerName = '',
        callerLineNo = 0,
        callerStartChar = 0,
        argsStartIndex = 0,
        argsEndIndex = 0;
      if (
        (res.captures.forEach((cap, capIndex) => {
          const node = cap.node;
          cap.name == 'caller'
            ? ((callerName = source.substring(node.startIndex, node.endIndex)),
              (callerLineNo = node.startPosition.row + linesBeforeTruncation),
              (callerStartChar = node.startPosition.column))
            : cap.name == 'args' && ((argsStartIndex = node.startIndex), (argsEndIndex = node.endIndex));
        }),
        offset >= argsStartIndex && offset <= argsEndIndex)
      ) {
        const callerLineCol = {
          line: callerLineNo,
          character: callerStartChar,
        };
        callers.push([callerName, callerLineCol]);
      }
    }),
    tree.delete(),
    callers.map(([name, position]) => ({
      name: name,
      position: position,
    }))
  );
}
function cursorContextOptions(options) {
  return {
    ...options,
  };
}
/**
 * 获取光标上下文信息。
 *
 * @param {Object} doc - 文档对象。
 * @param {Object} [options={}] - 选项对象。
 * @returns {Object} - 光标上下文信息对象。
 */
export function getCursorContext(doc, options = {}) {
  const completeOptions = cursorContextOptions(options);
  const tokenizer = getTokenizer(completeOptions.tokenizerName);
  if (completeOptions.maxLineCount !== void 0 && completeOptions.maxLineCount < 0)
    throw new Error('maxLineCount must be non-negative if defined');
  if (completeOptions.maxTokenLength !== void 0 && completeOptions.maxTokenLength < 0)
    throw new Error('maxTokenLength must be non-negative if defined');
  if (completeOptions.maxLineCount === 0 || completeOptions.maxTokenLength === 0)
    return {
      context: '',
      lineCount: 0,
      tokenLength: 0,
      tokenizerName: completeOptions.tokenizerName,
    };
  let context = doc.source.slice(0, doc.offset);
  return (
    completeOptions.maxLineCount !== void 0 &&
      (context = context
        .split(
          `
`
        )
        .slice(-completeOptions.maxLineCount).join(`
`)),
    completeOptions.maxTokenLength !== void 0 &&
      (context = tokenizer.takeLastLinesTokens(context, completeOptions.maxTokenLength)),
    {
      context: context,
      lineCount: context.split(`
`).length,
      tokenLength: tokenizer.tokenLength(context),
      tokenizerName: completeOptions.tokenizerName,
    }
  );
}

function getTokenizer(tokenizerName) {
  // 假设我们有一个简单的tokenizer，它根据空格分割字符串
  const simpleTokenizer = {
    takeLastLinesTokens: function (context, maxTokenLength) {
      const tokens = context.split(/\s+/);
      return tokens.slice(-maxTokenLength).join(' ');
    },
    tokenLength: function (context) {
      return context.split(/\s+/).length;
    },
  };

  // 根据tokenizerName返回不同的tokenizer
  // 这里只是一个示例，实际应用中可能有更复杂的逻辑
  switch (tokenizerName) {
    case 'simple':
      return simpleTokenizer;
    // 可以添加更多的tokenizer case
    default:
      throw new Error('Unknown tokenizer name');
  }
}

/**
 * 将vscode.Position转换为查询点对象。
 * @param position - 包含行和字符的vscode.Position对象。
 * @returns 包含起始点和结束点的对象。
 */
export function positionToQueryPoints(position: Pick<vscode.Position, 'line' | 'character'>) {
  const startPoint = {
    row: position.line,
    column: position.character,
  };

  const endPoint = {
    row: position.line,
    // 在触发位置后一个字符。
    column: position.character + 1,
  };

  return { startPoint, endPoint };
}
/**
 * 获取补全意图。
 * @param params - 包含文档和位置的参数。
 * @returns 补全意图的名称或空字符串。
 */
export async function getCompletionIntent(params) {
  const { document, position } = params || {};
  const source = document.getText();
  const tree = await parseTreeSitter(document.languageId, source);
  const positions = queryFunctions(document.languageId, tree.rootNode);
  const pst = positions.find((item) =>
    item.captures.find(
      (cap) => cap.node.startPosition.row <= position.line && cap.node.endPosition.row >= position.line
    )
  );
  const point =
    pst && pst.captures
      ? pst.captures.find(
          (cap) => cap.node.startPosition.row <= position.line && cap.node.endPosition.row >= position.line
        )
      : null;
  if (point) {
    return point.name;
  }
  return '';
}

interface LanguageConfig {
  blockStart: string;
  blockElseTest: RegExp;
  blockEnd: string | null;
  commentStart: string;
}

export function getLanguageConfig(languageId: string): LanguageConfig | null {
  switch (languageId) {
    case 'astro':
    case 'c':
    case 'cpp':
    case 'csharp':
    case 'dart':
    case 'go':
    case 'java':
    case 'javascript':
    case 'javascriptreact':
    case 'kotlin':
    case 'php':
    case 'rust':
    case 'svelte':
    case 'typescript':
    case 'typescriptreact':
    case 'vue':
      return {
        blockStart: '{',
        blockElseTest: /^[\t ]*} else/,
        blockEnd: '}',
        commentStart: `// `,
      };
    case 'python': {
      return {
        blockStart: ':',
        blockElseTest: /^[\t ]*(elif |else:)/,
        blockEnd: null,
        commentStart: `# `,
      };
    }
    case 'elixir': {
      return {
        blockStart: 'do',
        blockElseTest: /^[\t ]*(else|else do)/,
        blockEnd: 'end',
        commentStart: `# `,
      };
    }
    default:
      return null;
  }
}
/**
 * 异步查找并返回节点树中的所有类定义节点。
 * @param node - 要遍历的根节点。
 * @returns 包含所有类定义节点的数组。
 */
export async function findClassNodes(languageId, code) {
  try {
    const classes: any = [];
    const tree = await parseTreeSitter(languageId, code);
    const rootNode = tree.rootNode;
    const traverse = (node) => {
      try {
        if (
          node?.type === 'class_definition' ||
          node?.type === 'extension_declaration' ||
          node?.type === 'function_signature' ||
          node?.type === 'method_signature' ||
          node?.type === 'enum_declaration'
        ) {
          classes.push(node);
        }
        node?.namedChildren && node.namedChildren.forEach(traverse);
      } catch (error) {
        console.error('%c [ error ]-1234', 'font-size:13px; background:pink; color:#bf2c9f;', error);
      }
    };

    traverse(rootNode);
    return classes;
  } catch (error) {
    console.log('%c [ error ]-1232', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    return [];
  }
}
/**
 * 查找顶级声明。
 * @param languageId - 语言标识符。
 * @param code - 源代码。
 * @returns 顶级声明数组。
 */
export async function findTopLevelDeclarations(languageId, code) {
  try {
    const declarations: any = [];
    const tree = await parseTreeSitter(languageId, code);
    const rootNode = tree.rootNode;

    rootNode?.namedChildren &&
      rootNode.namedChildren.forEach((node) => {
        try {
          if (
            node.type === 'class_definition' ||
            node.type === 'extension_declaration' ||
            node.type === 'function_signature' ||
            node.type === 'method_signature' ||
            node.type === 'enum_declaration'
          ) {
            declarations.push(node);
          }
        } catch (error) {
          console.error('%c [ error ]-1234', 'font-size:13px; background:pink; color:#bf2c9f;', error);
        }
      });

    return declarations;
  } catch (error) {
    console.log('%c [ error ]-1232', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    return [];
  }
}

/**
 * 获取光标位置所在的函数名称。
 *
 * @param {Object} params - 参数对象。
 * @param {string} params.language - 代码语言。
 * @param {string} params.source - 源代码。
 * @param {Object} params.cursorPosition - 光标位置。
 * @returns {Promise<Object|null>} - 返回光标所在函数的位置信息或null。
 */
export async function getFunctionNameAtCursor({ language, source, cursorPosition }) {
  try {
    const fns = await getFunctionsInfo(language, source, cursorPosition);
    const element =
      fns.find(
        (element) =>
          element?.startPosition?.row <= cursorPosition.line && element?.endPosition?.row > cursorPosition.line
      ) || null;
    return element;
  } catch (error) {
    return null;
  }
}

/**
 * 递归获取函数名
 * @param node - 语法树节点
 * @returns 函数名或null
 */
function getFunctionName(node) {
  try {
    if (node.type === 'variable_declarator' && node.firstChild.type === 'identifier') {
      return node.firstChild.text;
    }
    for (let i = 0; i < node.childCount; i++) {
      const result = getFunctionName(node.child(i));
      if (result) {
        return result;
      }
    }
  } catch (error) {
    return null;
  }
}

/**
 * 获取函数体内容
 * @param node - 语法树节点
 * @param language - 编程语言
 * @param spaceString - 缩进字符串
 * @returns 函数体内容或null
 */
function getFunctionBody(node, language, spaceString) {
  // 检查不同语言的函数类型
  if (isFunction(language, node)) {
    // 找到函数体的起始和结束位置
    const bodyNode = node.namedChildren.find(
      (child) =>
        child.type === 'block' ||
        child.type === 'statement_block' ||
        child.type === 'expression_statement' ||
        child.type === 'compound_statement'
    );
    if (bodyNode) {
      let body = '';
      for (let index = 0; index < bodyNode.children.length; index++) {
        const child = bodyNode.children[index];
        const cfg = getLanguageConfig(language);
        if (child.type !== cfg?.blockStart && child.type !== cfg?.blockEnd) {
          body += spaceString + child.text + '\n';
        }
      }
      return body ?? bodyNode.text;
    }
  }

  // 递归遍历子节点
  for (let i = 0; i < node.namedChildCount; i++) {
    const result = getFunctionBody(node.namedChild(i), language, spaceString);
    if (result) {
      return result;
    }
  }
  return null;
}
/*
 * 获取指定语言源代码中的函数信息。
 * @param language - 源代码语言。
 * @param source - 源代码内容。
 * @returns 函数信息数组。
 */
export async function getFunctionsInfo(language, source, cursorPosition) {
  try {
    const tree = await parseTreeSitter(language, source);
    const positions = queryFunctions(language, tree.rootNode).map((res) => {
      const fn = res.captures.find((c) => c.name === 'function').node;
      if (fn.type == 'comment') {
        return null;
      }
      const range = fn.startPosition;
      const node = tree.rootNode.descendantForPosition({ ...range, column: 0 }, fn.endPosition);
      const functionName = getFunctionName(node);
      const selection: vscode.Selection = new vscode.Selection(
        fn.startPosition.row,
        0,
        fn.endPosition.row,
        fn.endPosition.column + 1
      );
      const editor = vscode.window.activeTextEditor;
      const textCursor = editor?.document?.getText(selection);
      const selectionTrailingString: vscode.Selection = new vscode.Selection(
        cursorPosition.line,
        0,
        cursorPosition.line,
        cursorPosition.character
      );
      const spaceString = editor?.document?.getText(selectionTrailingString);
      const fnBodyCursor = getFunctionBody(node, language, spaceString);
      return {
        functionContent: textCursor,
        functionBodyContent: fnBodyCursor,
        startPosition: fn.startPosition,
        endPosition: fn.endPosition,
        startIndex: fn.startIndex,
        endIndex: fn.endIndex,
        functionName,
      };
    });
    const psts = positions.filter((item) => !!item);
    return tree.delete(), psts;
  } catch (error) {
    return [];
  }
}
/**
 * 检查光标是否在函数或类中。
 * @returns {Promise<boolean>} 如果光标在函数或类中，返回 true，否则返回 false。
 */
export async function isCursorInFunctionOrClass() {
  try {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return false;
    const cursorPosition = editor.selection.active;
    const document = editor.document;
    const node = await parseTreeSitter(document.languageId, document.getText());
    const functionNodes = queryFunctions(document.languageId, node.rootNode);
    if (functionNodes.length > 0) {
      for (let index = 0; index < functionNodes.length; index++) {
        const element = functionNodes[index];
        const node = element?.captures?.find(
          (c) => c.name === 'function' || c.name === 'class' || c.name === 'methods'
        )?.node;
        if (node.type !== 'comment') {
          const isInFn =
            node?.startPosition?.row <= cursorPosition.line && node?.endPosition?.row > cursorPosition.line;
          return isInFn;
        }
      }
    }
    return false;
  } catch (error) {
    return false;
  }
}
